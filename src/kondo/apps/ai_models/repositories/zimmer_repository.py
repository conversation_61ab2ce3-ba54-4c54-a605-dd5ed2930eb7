import asyncio
import logging
from typing import Dict, Any, Optional
import httpx
from pydantic import BaseModel
from kondo.config import settings
from kondo.apps.ai_models.models import ModelConfig, ResponseType
from kondo.apps.ai_models.repositories.vault_client import VaultClient


logger = logging.getLogger(__name__)


class ToolConfig(BaseModel):
    """Tool configuration from Zimmer service"""

    name: str
    type: str
    description: str
    parameters: Optional[Dict[str, Any]] = None
    api_endpoint: Optional[str] = None
    api_key: Optional[str] = None
    headers: Optional[Dict[str, str]] = None
    timeout: Optional[int] = 30
    retries: Optional[int] = 3
    custom_config: Optional[Dict[str, Any]] = None
    enabled: bool = True


class ZimmerRepository:
    """Repository for fetching model and tool configurations from Zimmer service"""

    def __init__(
        self,
        team_id: int,
        api_key: str,
        base_url: str = settings.AUTOMATION_SERVER_HOST,
    ):
        self.base_url = base_url
        self._api_key = api_key
        self._team_id = team_id
        self.client = httpx.AsyncClient(
            base_url=base_url,
            timeout=httpx.Timeout(30.0, connect=5.0),
            headers={
                "Authorization": api_key.encode("utf-8"),
                "Content-Type": "application/json",
                "Accept": "application/json",
            },
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10),
        )
        self.vault_client = VaultClient()

    @property
    def default_params(self):
        """Return default query parameters for all requests"""
        return {"team_id": self._team_id}

    async def get_model_config(self, model_id: str) -> Optional[ModelConfig]:
        """Fetch model configuration from Zimmer service"""
        try:
            async with asyncio.timeout(30):  # Overall timeout for the operation
                response = await self.client.get(
                    f"/api/v1/ai_agent/agents/{model_id}", params=self.default_params
                )
                response.raise_for_status()

                data = response.json()

                return ModelConfig(
                    model=data["model"],
                    model_provider=data["model_provider"],
                    api_key=data["secret"]["data"]["api_key"],
                    temperature=data.get("temperature", 0.7),
                    max_tokens=data.get("max_tokens", 512),
                    retries=data.get("retries", 3),
                    timeout=data.get("timeout", 30),
                    stream=data.get("stream", False),
                    response_type=data.get("response_type", ResponseType.TEXT),
                    json_schema=data.get("response_schema"),
                    system_instructions=data.get("system_instructions"),
                    tools=data.get("tools"),
                )
        except asyncio.TimeoutError:
            logger.error(f"Timeout fetching model config for {model_id}")
            return None
        except httpx.HTTPStatusError as e:
            logger.error(
                f"HTTP error fetching model config: {e.response.status_code} - {e.response.text}"
            )
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error fetching model config: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching model config: {str(e)}")
            return None

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    async def __aenter__(self):
        """Support for async context manager"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Close client when exiting context"""
        await self.close()
