import hvac
import os
from typing import Optional


class VaultClient:
    def __init__(
        self,
        vault_addr: Optional[str] = None,
        vault_role: Optional[str] = None,
        kubernetes_token_path: Optional[str] = None,
    ):
        self.vault_addr = vault_addr if vault_addr else os.environ.get("VAULT_ADDR")
        self.vault_role = vault_role if vault_role else os.environ.get("VAULT_ROLE")
        self.kubernetes_token_path = (
            kubernetes_token_path
            if kubernetes_token_path
            else os.getenv(
                "KUBERNETES_TOKEN_PATH",
                "/var/run/secrets/kubernetes.io/serviceaccount/token",
            )
        )
        self._client: Optional[hvac.Client] = None

    def _initialize_hvac_client(self):
        with open(self.kubernetes_token_path, "r") as file:
            token = file.read().strip()

        client = hvac.Client(url=self.vault_addr)
        client.auth.kubernetes.login(role=self.vault_role, jwt=token)
        return client

    @property
    def client(self) -> hvac.Client:
        if not self._client:
            self._client = self._initialize_hvac_client()
        return self._client

    @staticmethod
    def _normalize_path(path: str) -> str:
        if path.startswith("kv/data/"):
            return path[len("kv/data/") :]
        return path

    def get_secret(self, path: str) -> str:
         return self.client.secrets.kv.v2.read_secret_version(
            mount_point="kv", path=self._normalize_path(path)
        )["data"]["data"]