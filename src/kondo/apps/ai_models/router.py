import json
from typing import As<PERSON><PERSON><PERSON>ator
from fastapi import APIRouter, HTTPException, Head<PERSON>
from fastapi.responses import StreamingResponse

from litellm import acompletion
from kondo.apps.ai_models.models import (
    ConversationRequest,
    ConversationResponse,
    ResponseType,
)
from kondo.apps.ai_models.repositories.zimmer_repository import ZimmerRepository

router = APIRouter()

PROVIDER_BASE_URLS = {
    "openai": "https://api.openai.com/v1",
    "anthropic": "https://api.anthropic.com/v1",
    "gemini": "https://generativelanguage.googleapis.com/v1beta",
    # Add more as needed
}


async def stream_response_litellm(
    config, messages, response_type
) -> AsyncGenerator[str, None]:
    """Stream LLM responses using LiteLLM"""
    async for chunk in acompletion(**config, messages=messages):
        if response_type == ResponseType.JSON_SCHEMA:
            yield json.dumps(chunk)
        else:
            delta = chunk.get("choices", [{}])[0].get("delta", {}).get("content", "")
            if delta:
                yield delta


def extract_tool_calls(llm_response):
    tool_calls = []
    choices = llm_response.get("choices", [])
    choice = choices[0] if choices else {}
    _tool_calls = choice.get("message", {}).get("tool_calls", [])
    if _tool_calls is not None:
        for tool_call in _tool_calls:
            toll_content = json.loads(tool_call.function.arguments)
            tool_calls.append(
                {"name": tool_call.function.name, "content": toll_content}
            )
    return tool_calls


@router.post("/{team_id}/conversation", response_model=ConversationResponse)
async def converse(
    team_id: int,
    request: ConversationRequest,
    authorization: str = Header(name="Authorization"),
):
    try:
        # Fetch model config from Zimmer or use provided
        if isinstance(request.model, int):
            async with ZimmerRepository(team_id, authorization) as zimmer_repo:
                model_config = await zimmer_repo.get_model_config(str(request.model))
                if not model_config:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Model configuration not found for {request.model}",
                    )
        else:
            model_config = request.model

        # Prepare message list for LiteLLM
        messages = []
        if model_config.system_instructions:
            messages.append(
                {"role": "system", "content": model_config.system_instructions}
            )
        if request.prompt:
            messages.append({"role": "user", "content": request.prompt})
        messages.append({"role": "user", "content": request.message})

        # Build config for LiteLLM
        llm_config = {
            "model": f"{model_config.model_provider}/{model_config.model}",
            "api_key": model_config.api_key,
            "temperature": request.temperature or model_config.temperature,
            "max_tokens": request.max_tokens or model_config.max_tokens,
            "tools": [],
        }

        response_type = request.response_type or model_config.response_type
        tools = model_config.tools
        json_schema = request.json_schema or model_config.json_schema

        if response_type == ResponseType.JSON_SCHEMA and json_schema:
            llm_config["tool_choice"] = "required"
            llm_config["tools"].append(
                {
                    "type": "function",
                    "function": {
                        "name": "structured_output",
                        "description": json_schema.get("description", ""),
                        "parameters": json_schema,
                    },
                }
            )
        if tools:
            llm_config["tools"].extend(
                [{"type": "function", "function": tool} for tool in tools]
            )

        # Streamed response
        if request.stream or model_config.stream:
            return StreamingResponse(
                stream_response_litellm(llm_config, messages, response_type),
                media_type="text/event-stream",
            )

        # Non-streamed response
        llm_response = await acompletion(**llm_config, messages=messages)

        # Extract result
        if "error" in llm_response:
            raise HTTPException(status_code=500, detail=llm_response["error"])

        choices = llm_response.get("choices", [])
        choice = choices[0] if choices else {}
        content = None
        tool_calls = []
        if response_type == ResponseType.JSON_SCHEMA:
            _tool_calls = extract_tool_calls(llm_response)
            for tool_call in _tool_calls:
                if tool_call["name"] == "structured_output":
                    content = tool_call["content"]
                    break
            else:
                tool_calls = _tool_calls

        else:
            content = choice.get("message", {}).get("content", None)
            tool_calls = extract_tool_calls(llm_response)

        return ConversationResponse(
            content=content,
            tool_calls=tool_calls,
            response_type=response_type,
            model_used=model_config.model,
            tokens_used=llm_response.get("usage", {}).get("total_tokens", 0),
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
