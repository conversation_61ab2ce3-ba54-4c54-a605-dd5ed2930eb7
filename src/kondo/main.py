from fastapi import FastAPI
from fastapi.routing import APIRoute
from kondo.apps.notifications.endpoints.pubsub import lifespan
from kondo.config import settings
from kondo.utils import import_string
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
import logging.config


logging.config.dictConfig(settings.LOGGING_CONFIG)


def custom_generate_unique_id(route: APIRoute):
    return f"{route.tags[0]}-{route.name}"


app = FastAPI(
    title=settings.PROJECT_NAME,
    lifespan=lifespan,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    generate_unique_id_function=custom_generate_unique_id,
)

app.add_middleware(
    CORSMiddleware,  # type: ignore
    allow_origins=["*"],  # type: ignore # Set to None to disable CORS
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(import_string("kondo.apps.api.router"))


def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    openapi_schema = get_openapi(
        title="WebSocket API Docs",
        version="1.0.0",
        description="This API provides WebSocket-based event subscriptions.",
        routes=app.routes,
    )
    docs = import_string("kondo.apps.notifications.openapi_docs.DOCS")
    openapi_schema["paths"].update(docs)

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

if __name__ == "__main__":
    from kondo.bin.kondo import cli_app

    cli_app()
