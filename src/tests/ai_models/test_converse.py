import os
from unittest import TestCase
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from dotenv import load_dotenv

from kondo.apps.api import router
from kondo.apps.ai_models.models import ModelConfig

# Load environment variables from .env file
load_dotenv()

# Test client
client = TestClient(router)

JSON_SCHEMA = {
    "title": "openai",
    "description": "Test desc.",
    "type": "object",
    "properties": {
        "rating": {
            "type": ["integer"],
            "minimum": 1,
            "maximum": 5,
            "description": "Rating from 1 to 5, or null if not applicable",
        },
        "explanation": {
            "type": "string",
            "description": "Brief explanation for the rating",
        },
    },
    "required": ["rating"],
}
# Test configurations for different models
MODEL_CONFIGS = [
    {
        "payload": {
            "name": "openai",
            "description": "Test desc.",
            "model_provider": "openai",
            "model": "mistralai/mistral-small-3.1-24b-instruct-2503",
            "api_key": "TEST_NVIDIA_API_KEY",
            "temperature": 0,
            "max_tokens": 512,
            "system_instructions": "You are a helpful assistant.",
            "tools": [
                #     {
                #     "name": "title",
                #     "description": "Determine weather in my location",
                #     "strict": True,
                #     "parameters": {
                #         "type": "object",
                #         "properties": {
                #             "location": {
                #                 "type": "string",
                #                 "description": "The city and state e.g. San Francisco, CA"
                #             },
                #             "unit": {
                #                 "type": "string",
                #                 "enum": [
                #                     "c",
                #                     "f"
                #                 ]
                #             }
                #         },
                #         "additionalProperties": False,
                #         "required": [
                #             "location",
                #             "unit"
                #         ]
                #     }
                # }
            ],
            "response_type": "json_schema",
            "json_schema": JSON_SCHEMA,
        },
        "prompt": "You are a helpful assistant.",
        "expected_response_contains": None,
        "message": "get weather in yerevan c",
    },
    {
        "payload": {
            "name": "openai",
            "description": "Test desc.",
            "model": "gpt-4",
            "model_provider": "openai",
            "api_key": "TEST_OPENAI_API_KEY",
            "temperature": 0,
            "max_tokens": 512,
            "system_instructions": "You are a helpful assistant.",
            "tools": [
                {
                    "name": "title",
                    "description": "Determine weather in my location",
                    "strict": True,
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "The city and state e.g. San Francisco, CA",
                            },
                            "unit": {"type": "string", "enum": ["c", "f"]},
                        },
                        "additionalProperties": False,
                        "required": ["location", "unit"],
                    },
                }
            ],
            "response_type": "json_schema",
            "json_schema": JSON_SCHEMA,
        },
        "prompt": "You are a helpful assistant.",
        "expected_response_contains": None,
        "message": "get weather in yerevan c",
    },
    {
        "payload": {
            "name": "anthropic",
            "model_provider": "anthropic",
            "description": "Test desc.",
            "model": "claude-3-opus-20240229",
            "api_key": "TEST_ANTHROPIC_API_KEY",
            "temperature": 0,
            "max_tokens": 512,
            "response_type": "json_schema",
            "json_schema": JSON_SCHEMA,
        },
        "prompt": "You are a helpful assistant.",
        "expected_response_contains": None,
        "message": "Bla bla bla",
    },
    {
        "payload": {
            "name": "gemini",
            "model_provider": "gemini",
            "description": "Test desc.",
            "model": "google_genai:gemini-2.5-pro",
            "api_key": "TEST_GEMINI_API_KEY",
            "temperature": 0,
            "max_tokens": 512,
            "response_type": "json_schema",
            "json_schema": JSON_SCHEMA,
        },
        "prompt": "You are a helpful assistant.",
        "expected_response_contains": None,
        "message": "Bla bla bla",
    },
]


class MockLLMResponse:
    """Mock LLM response object"""

    def __init__(self, content, tokens_used=100):
        self.content = content
        self.tokens_used = tokens_used
        self.messages = [MagicMock(content=content)]
        self.error = None


class TestAIModelsAPI(TestCase):
    """Test cases for AI Models API"""

    @classmethod
    def setUpClass(cls):
        """Set up test environment once before all tests"""
        # Store API keys from environment variables
        cls.api_keys = {}
        for config in MODEL_CONFIGS:
            api_key = os.environ.get(config["payload"]["api_key"])
            if not api_key:
                print("Warning: api_key not found in environment variables.")
            config["payload"]["api_key"] = api_key

    def setUp(self):
        """Set up before each test"""
        # Create patches for ZimmerRepository and LLM
        self.zimmer_patch = patch("kondo.apps.ai_models.router.ZimmerRepository")

        # Start patches
        self.mock_zimmer = self.zimmer_patch.start()

        # Configure mock ZimmerRepository
        self.mock_zimmer_instance = self.mock_zimmer.return_value
        self.mock_zimmer_instance.__aenter__.return_value = self.mock_zimmer_instance
        self.mock_zimmer_instance.__aexit__.return_value = None

        # Create mock LLM instance
        self.mock_llm = AsyncMock()

    def tearDown(self):
        """Clean up after each test"""
        # Stop patches
        self.zimmer_patch.stop()

    def test_conversation_with_all_models(self):
        """Test conversation endpoint with all configured models"""
        team_id = 123

        for m_config in MODEL_CONFIGS:
            config = m_config["payload"]
            with self.subTest(model=config["name"]):
                model_config = ModelConfig(**config)
                self.mock_zimmer_instance.get_model_config = AsyncMock(
                    return_value=model_config
                )

                # Create request payload
                payload = {
                    "message": m_config["message"],
                    "model": 1,
                    "prompt": m_config["prompt"],
                    # "stream": True
                }

                response = client.post(
                    f"/api/v1/{team_id}/conversation",
                    json=payload,
                    headers={"X-API-Key": "test-api-key"},
                )

                self.assertEqual(response.status_code, 200)
                print(response.content)
